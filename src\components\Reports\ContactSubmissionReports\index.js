'use client';
import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import ContactSubmissionReportsTable from './ContactSubmissionReportsTable';
import { staticOptions } from '@/helper/common/staticOptions';
import '../reports.scss';

export default function ContactSubmissionReports({ onFiltersUpdate }) {
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState({
    dateRange: '',
  });

  // Pagination state from table
  const [paginationState, setPaginationState] = useState({
    page: 1,
    rowsPerPage: 10,
    totalCount: 0,
  });

  // Filter fields configuration
  const filterFields = [
    {
      name: 'search',
      type: 'search',
      label: 'Search by Name',
      searchclass: 'search-field-wrapper',
    },
    {
      name: 'dateRange',
      type: 'select',
      label: 'Date Range',
      options: staticOptions?.ANALYTICS_DATE_RANGES,
    },
  ];

  // Handle pagination state updates from table
  const handlePaginationUpdate = (newPaginationState) => {
    setPaginationState(newPaginationState);

    // Update parent with current filters including new pagination state
    if (onFiltersUpdate) {
      const exportFilters = {
        search: searchValue || '',
        recipe_name: searchValue || '', // Map search to recipe_name
        user_email: filters?.userEmail || '',
        date_range: filters?.dateRange || '',
        page: newPaginationState.page, // Use new pagination state
        size: newPaginationState.rowsPerPage, // Use new pagination state
        totalCount: newPaginationState.totalCount || 0, // Keep totalCount for reference
      };

      onFiltersUpdate(exportFilters);
    }
  };

  // Handle filter apply
  const handleFilterApply = (filterValues) => {
    setFilters(filterValues);
    setSearchValue(filterValues?.search || '');

    // Prepare filters for export with current pagination
    const exportFilters = {
      search: filterValues?.search || '',
      recipe_name: filterValues?.search || '', // Map search to recipe_name
      user_email: filterValues?.userEmail || '',
      date_range: filterValues?.dateRange || '',
      page: paginationState.page, // Pass current page for export
      size: paginationState.rowsPerPage, // Pass current page size for export
      totalCount: paginationState.totalCount || 0, // Keep totalCount for reference
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    if (fieldName === 'search') {
      setSearchValue(value);
    }
  };

  // Update parent with initial filters on mount only
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        search: '',
        recipe_name: '',
        user_email: '',
        date_range: '',
        page: 1, // Initial page
        size: 10, // Initial page size
        totalCount: 0, // Initial total count
      };

      onFiltersUpdate(exportFilters);
    }
  }, [onFiltersUpdate]); // Only run when onFiltersUpdate changes (component mount)

  return (
    <Box className="report-main-container">
      {/* Filter Section */}
      <FilterCollapse
        fields={filterFields}
        onApply={handleFilterApply}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      {/* Table Section */}
      <ContactSubmissionReportsTable
        searchValue={searchValue}
        filters={filters}
        onPaginationUpdate={handlePaginationUpdate}
      />
    </Box>
  );
}
